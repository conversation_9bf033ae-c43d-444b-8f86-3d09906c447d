import Foundation
import UIKit

// MARK: - AI Document Service
class AIDocumentService {
    nonisolated(unsafe) static let shared = AIDocumentService()
    
    private let openAIAPIKey: String
    private let openAIBaseURL = "https://api.openai.com/v1/chat/completions"
    
    private init() {
        // Try to get API key from environment or use placeholder
        if let apiKey = ProcessInfo.processInfo.environment["OPENAI_API_KEY"], !apiKey.isEmpty {
            self.openAIAPIKey = apiKey
        } else {
            // For demo purposes, use a placeholder
            // In production, this should be properly configured
            self.openAIAPIKey = "sk-placeholder-key-replace-with-real-key"
            print("⚠️ Warning: Using placeholder OpenAI API key. Set OPENAI_API_KEY environment variable.")
        }
    }
    
    // MARK: - Process Document with AI
    func processDocument(image: UIImage) async throws -> ExtractedExpenseData {
        // Validate API key
        guard !openAIAPIKey.contains("placeholder") else {
            throw AIDocumentError.noAPIKey
        }

        // Convert image to base64
        guard let imageData = image.jpegData(compressionQuality: 0.8) else {
            throw AIDocumentError.imageProcessingFailed
        }
        
        let base64Image = imageData.base64EncodedString()
        
        // Create OpenAI Vision API request
        let request = try await createVisionAPIRequest(base64Image: base64Image)
        
        // Send request to OpenAI
        let response = try await sendRequest(request)
        
        // Parse response and extract expense data
        return try await parseExpenseData(from: response)
    }
    
    // MARK: - Create Vision API Request
    private func createVisionAPIRequest(base64Image: String) async throws -> URLRequest {
        guard let url = URL(string: openAIBaseURL) else {
            throw AIDocumentError.invalidURL
        }
        
        var request = URLRequest(url: url)
        request.httpMethod = "POST"
        request.setValue("application/json", forHTTPHeaderField: "Content-Type")
        request.setValue("Bearer \(openAIAPIKey)", forHTTPHeaderField: "Authorization")
        
        let prompt = """
        Analyze this receipt/invoice image and extract the following information in JSON format:
        {
            "vendor_name": "Name of the business/vendor",
            "total_amount": "Total amount paid (number only, no currency)",
            "vat_amount": "VAT/tax amount if shown separately (number only)",
            "date": "Transaction date in YYYY-MM-DD format",
            "description": "Brief description of the expense",
            "category": "One of: office_supplies, travel, utilities, rent, professional_services, marketing, equipment, other",
            "confidence": "Confidence score from 0.0 to 1.0"
        }
        
        Important:
        - Extract amounts as numbers only (e.g., "123.45" not "₪123.45")
        - Use Hebrew vendor names if the receipt is in Hebrew
        - If date is unclear, use today's date
        - Choose the most appropriate category
        - Be conservative with confidence scores
        """
        
        let payload: [String: Any] = [
            "model": "gpt-4-vision-preview",
            "messages": [
                [
                    "role": "user",
                    "content": [
                        [
                            "type": "text",
                            "text": prompt
                        ],
                        [
                            "type": "image_url",
                            "image_url": [
                                "url": "data:image/jpeg;base64,\(base64Image)"
                            ]
                        ]
                    ]
                ]
            ],
            "max_tokens": 500,
            "temperature": 0.1
        ]
        
        request.httpBody = try JSONSerialization.data(withJSONObject: payload)
        return request
    }
    
    // MARK: - Send Request
    private func sendRequest(_ request: URLRequest) async throws -> [String: Any] {
        let (data, response) = try await URLSession.shared.data(for: request)
        
        guard let httpResponse = response as? HTTPURLResponse else {
            throw AIDocumentError.invalidResponse
        }
        
        guard httpResponse.statusCode == 200 else {
            throw AIDocumentError.apiError(httpResponse.statusCode)
        }
        
        guard let json = try JSONSerialization.jsonObject(with: data) as? [String: Any] else {
            throw AIDocumentError.invalidJSON
        }
        
        return json
    }
    
    // MARK: - Parse Expense Data
    private func parseExpenseData(from response: [String: Any]) async throws -> ExtractedExpenseData {
        guard let choices = response["choices"] as? [[String: Any]],
              let firstChoice = choices.first,
              let message = firstChoice["message"] as? [String: Any],
              let content = message["content"] as? String else {
            throw AIDocumentError.invalidResponse
        }
        
        // Extract JSON from the response content
        guard let jsonData = extractJSON(from: content),
              let expenseJSON = try JSONSerialization.jsonObject(with: jsonData) as? [String: Any] else {
            throw AIDocumentError.invalidJSON
        }
        
        // Parse the extracted data
        let vendorName = expenseJSON["vendor_name"] as? String ?? ""
        let totalAmount = expenseJSON["total_amount"] as? String ?? "0"
        let vatAmount = expenseJSON["vat_amount"] as? String
        let dateString = expenseJSON["date"] as? String ?? ""
        let description = expenseJSON["description"] as? String ?? ""
        let categoryString = expenseJSON["category"] as? String ?? "other"
        let confidence = expenseJSON["confidence"] as? Double ?? 0.0
        
        // Parse date
        let dateFormatter = DateFormatter()
        dateFormatter.dateFormat = "yyyy-MM-dd"
        let date = dateFormatter.date(from: dateString) ?? Date()
        
        // Parse category
        let category = parseCategory(from: categoryString)
        
        return ExtractedExpenseData(
            vendorName: vendorName,
            totalAmount: totalAmount,
            vatAmount: vatAmount,
            date: date,
            category: category,
            description: description,
            confidence: confidence
        )
    }
    
    // MARK: - Helper Methods
    private func extractJSON(from content: String) -> Data? {
        // Look for JSON content between ```json and ``` or just find the JSON object
        let patterns = [
            "```json\\s*([\\s\\S]*?)\\s*```",
            "```\\s*([\\s\\S]*?)\\s*```",
            "\\{[\\s\\S]*\\}"
        ]
        
        for pattern in patterns {
            if let regex = try? NSRegularExpression(pattern: pattern, options: []),
               let match = regex.firstMatch(in: content, options: [], range: NSRange(content.startIndex..., in: content)) {
                let range = match.range(at: match.numberOfRanges > 1 ? 1 : 0)
                if let swiftRange = Range(range, in: content) {
                    let jsonString = String(content[swiftRange])
                    return jsonString.data(using: .utf8)
                }
            }
        }
        
        return content.data(using: .utf8)
    }
    
    private func parseCategory(from categoryString: String) -> ExpenseCategory {
        switch categoryString.lowercased() {
        case "office_supplies":
            return .officeSupplies
        case "travel":
            return .travel
        case "utilities":
            return .utilities
        case "rent":
            return .rent
        case "professional_services":
            return .professionalServices
        case "marketing":
            return .marketing
        case "equipment":
            return .equipment
        default:
            return .other
        }
    }
}

// MARK: - AI Document Errors
enum AIDocumentError: LocalizedError {
    case imageProcessingFailed
    case invalidURL
    case invalidResponse
    case invalidJSON
    case apiError(Int)
    case noAPIKey
    
    var errorDescription: String? {
        switch self {
        case .imageProcessingFailed:
            return "Failed to process image"
        case .invalidURL:
            return "Invalid API URL"
        case .invalidResponse:
            return "Invalid API response"
        case .invalidJSON:
            return "Invalid JSON format"
        case .apiError(let code):
            return "API error with code: \(code)"
        case .noAPIKey:
            return "No API key provided"
        }
    }
}
