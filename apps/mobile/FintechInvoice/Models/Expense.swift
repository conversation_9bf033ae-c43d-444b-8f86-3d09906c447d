import Foundation
import UIKit

// MARK: - Expense Model
struct Expense: Identifiable, Codable {
    let id: String
    let companyId: String
    let expenseNumber: String
    let vendorName: String
    let expenseDate: String
    let amount: Double
    let vatAmount: Double
    let totalAmount: Double
    let currency: String
    let category: ExpenseCategory
    let description: String?
    let status: ExpenseStatus
    let duplicateRisk: DuplicateRisk
    let duplicateOfId: String?
    let source: ExpenseSource
    let sourceEmailId: String?
    let originalFileUrl: String?
    let extractedData: String? // Store as JSON string for Codable compliance
    let approvedBy: String?
    let approvedAt: String?
    let rejectionReason: String?
    let createdAt: String
    let updatedAt: String
    
    // Computed properties for display
    var displayVendorName: String {
        return vendorName
    }
    
    var formattedAmount: String {
        let formatter = NumberFormatter()
        formatter.numberStyle = .currency
        formatter.currencyCode = currency
        formatter.currencySymbol = "₪"
        return formatter.string(from: NSNumber(value: totalAmount)) ?? "₪\(totalAmount)"
    }
    
    var formattedDate: String {
        let dateFormatter = DateFormatter()
        dateFormatter.dateFormat = "yyyy-MM-dd"
        if let date = dateFormatter.date(from: expenseDate) {
            dateFormatter.dateFormat = "dd/MM/yyyy"
            return dateFormatter.string(from: date)
        }
        return expenseDate
    }
    
    var statusDisplayText: String {
        switch status {
        case .pending:
            return "ממתין לאישור"
        case .approved:
            return "אושר"
        case .rejected:
            return "נדחה"
        }
    }
    
    var categoryDisplayText: String {
        switch category {
        case .officeSupplies:
            return "ציוד משרדי"
        case .travel:
            return "נסיעות"
        case .utilities:
            return "שירותים"
        case .rent:
            return "שכירות"
        case .professionalServices:
            return "שירותים מקצועיים"
        case .marketing:
            return "שיווק"
        case .equipment:
            return "ציוד"
        case .other:
            return "אחר"
        }
    }
    
    // Coding keys to map Swift property names to database field names
    enum CodingKeys: String, CodingKey {
        case id
        case companyId = "company_id"
        case expenseNumber = "expense_number"
        case vendorName = "vendor_name"
        case expenseDate = "expense_date"
        case amount
        case vatAmount = "vat_amount"
        case totalAmount = "total_amount"
        case currency
        case category
        case description
        case status
        case duplicateRisk = "duplicate_risk"
        case duplicateOfId = "duplicate_of_id"
        case source
        case sourceEmailId = "source_email_id"
        case originalFileUrl = "original_file_url"
        case extractedData = "extracted_data"
        case approvedBy = "approved_by"
        case approvedAt = "approved_at"
        case rejectionReason = "rejection_reason"
        case createdAt = "created_at"
        case updatedAt = "updated_at"
    }
}

// MARK: - Expense Enums
enum ExpenseCategory: String, Codable, CaseIterable {
    case officeSupplies = "office_supplies"
    case travel = "travel"
    case utilities = "utilities"
    case rent = "rent"
    case professionalServices = "professional_services"
    case marketing = "marketing"
    case equipment = "equipment"
    case other = "other"
}

enum ExpenseStatus: String, Codable, CaseIterable {
    case pending = "pending"
    case approved = "approved"
    case rejected = "rejected"
}

enum DuplicateRisk: String, Codable, CaseIterable {
    case none = "none"
    case low = "low"
    case high = "high"
}

enum ExpenseSource: String, Codable, CaseIterable {
    case manual = "manual"
    case camera = "camera"
    case emailScan = "email_scan"
    case upload = "upload"
}

// MARK: - Create Expense Request
struct CreateExpenseRequest: Codable {
    let companyId: String
    let vendorName: String
    let expenseDate: String
    let amount: Double
    let vatAmount: Double
    let totalAmount: Double
    let currency: String
    let category: ExpenseCategory
    let description: String?
    let source: ExpenseSource
    let originalFileUrl: String?
    
    enum CodingKeys: String, CodingKey {
        case companyId = "company_id"
        case vendorName = "vendor_name"
        case expenseDate = "expense_date"
        case amount
        case vatAmount = "vat_amount"
        case totalAmount = "total_amount"
        case currency
        case category
        case description
        case source
        case originalFileUrl = "original_file_url"
    }
}

// MARK: - Expense Form Data
class ExpenseFormData: ObservableObject {
    @Published var vendorName: String = ""
    @Published var amount: String = ""
    @Published var vatAmount: String = ""
    @Published var totalAmount: String = ""
    @Published var expenseDate = Date()
    @Published var category: ExpenseCategory = .other
    @Published var description: String = ""
    @Published var selectedImage: UIImage?
    @Published var selectedDocumentURL: URL?
    
    var isValid: Bool {
        return !vendorName.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty &&
               !amount.isEmpty &&
               Double(amount) != nil &&
               Double(amount)! > 0
    }
    
    func calculateTotals(with inputAmount: String? = nil) {
        // Use the provided amount or fall back to the stored amount
        let amountToUse = inputAmount ?? amount

        // Clean the amount string and parse it properly
        let cleanAmount = amountToUse.trimmingCharacters(in: .whitespacesAndNewlines)

        // Use NumberFormatter for proper locale-aware parsing
        let formatter = NumberFormatter()
        formatter.numberStyle = .decimal
        formatter.locale = Locale(identifier: "en_US") // Force US locale for consistent parsing

        guard let number = formatter.number(from: cleanAmount),
              let amountValue = Double(exactly: number),
              amountValue > 0 else {
            // Fallback to simple Double parsing
            guard let fallbackValue = Double(cleanAmount), fallbackValue > 0 else {
                vatAmount = ""
                totalAmount = ""
                return
            }

            // Calculate with fallback value
            let vatValue = fallbackValue * 0.18
            vatAmount = String(format: "%.2f", vatValue)

            let totalValue = fallbackValue + vatValue
            totalAmount = String(format: "%.2f", totalValue)

            print("✅ Expense calculation (fallback): Input='\(cleanAmount)' → Amount=\(fallbackValue), VAT=\(vatValue), Total=\(totalValue)")
            return
        }

        // Calculate VAT (18%)
        let vatValue = amountValue * 0.18
        vatAmount = String(format: "%.2f", vatValue)

        // Calculate total
        let totalValue = amountValue + vatValue
        totalAmount = String(format: "%.2f", totalValue)

        print("✅ Expense calculation: Input='\(cleanAmount)' → Amount=\(amountValue), VAT=\(vatValue), Total=\(totalValue)")
    }
    
    func toCreateRequest(companyId: String, fileUrl: String?) -> CreateExpenseRequest {
        let amountValue = Double(amount) ?? 0
        let vatValue = Double(vatAmount) ?? 0
        let totalValue = Double(totalAmount) ?? 0
        
        let dateFormatter = DateFormatter()
        dateFormatter.dateFormat = "yyyy-MM-dd"
        
        return CreateExpenseRequest(
            companyId: companyId,
            vendorName: vendorName.trimmingCharacters(in: .whitespacesAndNewlines),
            expenseDate: dateFormatter.string(from: expenseDate),
            amount: amountValue,
            vatAmount: vatValue,
            totalAmount: totalValue,
            currency: "ILS",
            category: category,
            description: description.isEmpty ? nil : description.trimmingCharacters(in: .whitespacesAndNewlines),
            source: .upload,
            originalFileUrl: fileUrl
        )
    }
    
    func reset() {
        vendorName = ""
        amount = ""
        vatAmount = ""
        totalAmount = ""
        expenseDate = Date()
        category = .other
        description = ""
        selectedImage = nil
        selectedDocumentURL = nil
    }
}

// MARK: - Extracted Expense Data
struct ExtractedExpenseData: Codable {
    var vendorName: String
    var totalAmount: String
    var vatAmount: String?
    var date: Date
    var category: ExpenseCategory
    var description: String
    var confidence: Double // AI confidence score (0.0 - 1.0)

    init(
        vendorName: String = "",
        totalAmount: String = "",
        vatAmount: String? = nil,
        date: Date = Date(),
        category: ExpenseCategory = .other,
        description: String = "",
        confidence: Double = 0.0
    ) {
        self.vendorName = vendorName
        self.totalAmount = totalAmount
        self.vatAmount = vatAmount
        self.date = date
        self.category = category
        self.description = description
        self.confidence = confidence
    }
}
