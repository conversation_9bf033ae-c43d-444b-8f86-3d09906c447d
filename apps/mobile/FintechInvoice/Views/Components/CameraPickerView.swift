import SwiftUI
import UIKit
import AVFoundation
import UniformTypeIdentifiers

// MARK: - Camera Picker View
struct CameraPickerView: UIViewControllerRepresentable {
    @Binding var selectedImage: UIImage?
    @Binding var isPresented: Bool
    
    func makeUIViewController(context: Context) -> UIImagePickerController {
        let picker = UIImagePickerController()
        picker.delegate = context.coordinator
        picker.sourceType = .camera
        picker.allowsEditing = true
        return picker
    }
    
    func updateUIViewController(_ uiViewController: UIImagePickerController, context: Context) {}
    
    func makeCoordinator() -> Coordinator {
        Coordinator(self)
    }
    
    class Coordinator: NSObject, UIImagePickerControllerDelegate, UINavigationControllerDelegate {
        let parent: CameraPickerView
        
        init(_ parent: CameraPickerView) {
            self.parent = parent
        }
        
        func imagePickerController(_ picker: UIImagePickerController, didFinishPickingMediaWithInfo info: [UIImagePickerController.InfoKey : Any]) {
            if let editedImage = info[.editedImage] as? UIImage {
                parent.selectedImage = editedImage
            } else if let originalImage = info[.originalImage] as? UIImage {
                parent.selectedImage = originalImage
            }
            
            parent.isPresented = false
        }
        
        func imagePickerControllerDidCancel(_ picker: UIImagePickerController) {
            parent.isPresented = false
        }
    }
}

// MARK: - Photo Library Picker View
struct PhotoLibraryPickerView: UIViewControllerRepresentable {
    @Binding var selectedImage: UIImage?
    @Binding var isPresented: Bool
    
    func makeUIViewController(context: Context) -> UIImagePickerController {
        let picker = UIImagePickerController()
        picker.delegate = context.coordinator
        picker.sourceType = .photoLibrary
        picker.allowsEditing = true
        return picker
    }
    
    func updateUIViewController(_ uiViewController: UIImagePickerController, context: Context) {}
    
    func makeCoordinator() -> Coordinator {
        Coordinator(self)
    }
    
    class Coordinator: NSObject, UIImagePickerControllerDelegate, UINavigationControllerDelegate {
        let parent: PhotoLibraryPickerView
        
        init(_ parent: PhotoLibraryPickerView) {
            self.parent = parent
        }
        
        func imagePickerController(_ picker: UIImagePickerController, didFinishPickingMediaWithInfo info: [UIImagePickerController.InfoKey : Any]) {
            if let editedImage = info[.editedImage] as? UIImage {
                parent.selectedImage = editedImage
            } else if let originalImage = info[.originalImage] as? UIImage {
                parent.selectedImage = originalImage
            }
            
            parent.isPresented = false
        }
        
        func imagePickerControllerDidCancel(_ picker: UIImagePickerController) {
            parent.isPresented = false
        }
    }
}

// MARK: - Document Picker View
struct DocumentPickerView: UIViewControllerRepresentable {
    @Binding var selectedDocumentURL: URL?
    @Binding var isPresented: Bool
    
    func makeUIViewController(context: Context) -> UIDocumentPickerViewController {
        let picker = UIDocumentPickerViewController(forOpeningContentTypes: [
            UTType.pdf,
            UTType.jpeg,
            UTType.png,
            UTType.image
        ])
        picker.delegate = context.coordinator
        picker.allowsMultipleSelection = false
        return picker
    }
    
    func updateUIViewController(_ uiViewController: UIDocumentPickerViewController, context: Context) {}
    
    func makeCoordinator() -> Coordinator {
        Coordinator(self)
    }
    
    class Coordinator: NSObject, UIDocumentPickerDelegate {
        let parent: DocumentPickerView
        
        init(_ parent: DocumentPickerView) {
            self.parent = parent
        }
        
        func documentPicker(_ controller: UIDocumentPickerViewController, didPickDocumentsAt urls: [URL]) {
            guard let url = urls.first else { return }
            
            // Start accessing security-scoped resource
            if url.startAccessingSecurityScopedResource() {
                defer { url.stopAccessingSecurityScopedResource() }
                parent.selectedDocumentURL = url
            }
            
            parent.isPresented = false
        }
        
        func documentPickerWasCancelled(_ controller: UIDocumentPickerViewController) {
            parent.isPresented = false
        }
    }
}

// MARK: - Image Source Action Sheet
struct ImageSourceActionSheet: View {
    @Binding var showingCamera: Bool
    @Binding var showingPhotoLibrary: Bool
    @Binding var showingDocumentPicker: Bool
    @Binding var isPresented: Bool
    
    var body: some View {
        VStack(spacing: .spacing4) {
            Text("בחר מקור")
                .font(.hebrewHeading.weight(.semibold))
                .foregroundColor(.cardForeground)
                .padding(.top, .spacing4)
            
            VStack(spacing: .spacing3) {
                Button(action: {
                    isPresented = false
                    showingCamera = true
                }) {
                    HStack {
                        Image(systemName: "camera.fill")
                            .foregroundColor(.primary)
                        
                        Text("צלם תמונה")
                            .font(.hebrewBody.weight(.medium))
                            .foregroundColor(.cardForeground)
                        
                        Spacer()
                    }
                    .padding(.spacing4)
                    .background(Color.card)
                    .clipShape(RoundedRectangle(cornerRadius: .radiusMedium))
                }
                
                Button(action: {
                    isPresented = false
                    showingPhotoLibrary = true
                }) {
                    HStack {
                        Image(systemName: "photo.fill")
                            .foregroundColor(.primary)
                        
                        Text("בחר מהגלריה")
                            .font(.hebrewBody.weight(.medium))
                            .foregroundColor(.cardForeground)
                        
                        Spacer()
                    }
                    .padding(.spacing4)
                    .background(Color.card)
                    .clipShape(RoundedRectangle(cornerRadius: .radiusMedium))
                }
                
                Button(action: {
                    isPresented = false
                    showingDocumentPicker = true
                }) {
                    HStack {
                        Image(systemName: "doc.fill")
                            .foregroundColor(.primary)
                        
                        Text("בחר מסמך")
                            .font(.hebrewBody.weight(.medium))
                            .foregroundColor(.cardForeground)
                        
                        Spacer()
                    }
                    .padding(.spacing4)
                    .background(Color.card)
                    .clipShape(RoundedRectangle(cornerRadius: .radiusMedium))
                }
            }
            
            Button("ביטול") {
                isPresented = false
            }
            .buttonStyle(CosmicSecondaryButtonStyle())
            .padding(.top, .spacing4)
        }
        .padding(.spacing4)
        .background(Color.background)
        .clipShape(RoundedRectangle(cornerRadius: .radiusLarge))
        .padding(.spacing4)
    }
}

// MARK: - Camera Permission Helper
@MainActor
class CameraPermissionHelper: ObservableObject {
    @Published var hasPermission = false
    @Published var showingPermissionAlert = false
    
    func checkCameraPermission() {
        switch AVCaptureDevice.authorizationStatus(for: .video) {
        case .authorized:
            hasPermission = true
        case .notDetermined:
            requestCameraPermission()
        case .denied, .restricted:
            hasPermission = false
            showingPermissionAlert = true
        @unknown default:
            hasPermission = false
        }
    }
    
    private func requestCameraPermission() {
        AVCaptureDevice.requestAccess(for: .video) { granted in
            Task { @MainActor in
                self.hasPermission = granted
                if !granted {
                    self.showingPermissionAlert = true
                }
            }
        }
    }
}
