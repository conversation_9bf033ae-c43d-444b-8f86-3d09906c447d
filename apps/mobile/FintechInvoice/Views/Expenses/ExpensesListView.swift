import SwiftUI

struct ExpensesListView: View {
    @ObservedObject var expenseViewModel: ExpenseViewModel
    
    var body: some View {
        VStack(spacing: .spacing4) {
            // Header with stats
            ExpensesHeaderView(expenseViewModel: expenseViewModel)
            
            // Action buttons
            HStack(spacing: .spacing3) {
                But<PERSON>("סרוק קבלה") {
                    expenseViewModel.createExpenseFromCamera()
                }
                .buttonStyle(CosmicPrimaryButtonStyle())
                .frame(maxWidth: .infinity)
                
                Button("העלה מסמך") {
                    expenseViewModel.createExpenseFromUpload()
                }
                .buttonStyle(CosmicSecondaryButtonStyle())
                .frame(maxWidth: .infinity)
            }
            .padding(.horizontal, .spacing4)
            
            // Expenses list
            if expenseViewModel.isLoading {
                ProgressView("טוען הוצאות...")
                    .frame(maxWidth: .infinity, maxHeight: .infinity)
            } else {
                ScrollView {
                    LazyVStack(spacing: .spacing3) {
                        ForEach(expenseViewModel.expenses) { expense in
                            ExpenseRowView(expense: expense)
                        }
                    }
                    .padding(.horizontal, .spacing4)
                }
            }
        }
        .background(Color.background)
    }
}

// MARK: - Expenses Header View
struct ExpensesHeaderView: View {
    @ObservedObject var expenseViewModel: ExpenseViewModel
    
    var body: some View {
        VStack(spacing: .spacing4) {
            // Title
            Text("הוצאות")
                .font(.hebrewHeading.weight(.bold))
                .foregroundColor(.cardForeground)
            
            // Stats cards
            HStack(spacing: .spacing3) {
                StatCard(
                    title: "ממתינות לאישור",
                    value: "\(expenseViewModel.pendingExpensesCount)",
                    subtitle: "לעיבוד",
                    icon: "clock.fill",
                    color: .orange
                )

                StatCard(
                    title: "סה\"כ הוצאות",
                    value: expenseViewModel.formattedTotalAmount,
                    subtitle: "החודש",
                    icon: "creditcard.fill",
                    color: .green
                )
            }
            .padding(.horizontal, .spacing4)
        }
        .padding(.top, .spacing4)
    }
}



// MARK: - Expense Row View
struct ExpenseRowView: View {
    let expense: Expense
    
    var body: some View {
        VStack(spacing: .spacing3) {
            // Header row
            HStack {
                VStack(alignment: .trailing, spacing: .spacing1) {
                    Text(expense.displayVendorName)
                        .font(.hebrewBody.weight(.medium))
                        .foregroundColor(.cardForeground)
                        .lineLimit(1)
                    
                    Text(expense.formattedDate)
                        .font(.hebrewCaption)
                        .foregroundColor(.mutedForeground)
                }
                
                Spacer()
                
                VStack(alignment: .leading, spacing: .spacing1) {
                    Text(expense.formattedAmount)
                        .font(.hebrewBody.weight(.semibold))
                        .foregroundColor(.cardForeground)
                    
                    StatusBadge(status: expense.status)
                }
            }
            
            // Category and description
            HStack {
                if let description = expense.description, !description.isEmpty {
                    Text(description)
                        .font(.hebrewCaption)
                        .foregroundColor(.mutedForeground)
                        .lineLimit(2)
                }
                
                Spacer()
                
                CategoryBadge(category: expense.category)
            }
            
            // Document indicator
            if expense.originalFileUrl != nil {
                HStack {
                    Spacer()
                    
                    HStack(spacing: .spacing1) {
                        Image(systemName: "paperclip")
                            .font(.system(size: 12))
                            .foregroundColor(.mutedForeground)
                        
                        Text("מסמך מצורף")
                            .font(.hebrewCaption)
                            .foregroundColor(.mutedForeground)
                    }
                }
            }
        }
        .padding(.spacing4)
        .background(Color.card)
        .clipShape(RoundedRectangle(cornerRadius: .radiusMedium))
        .overlay(
            RoundedRectangle(cornerRadius: .radiusMedium)
                .stroke(Color.border, lineWidth: 1)
        )
    }
}

// MARK: - Status Badge
struct StatusBadge: View {
    let status: ExpenseStatus
    
    var body: some View {
        Text(statusText)
            .font(.hebrewCaption.weight(.medium))
            .foregroundColor(statusColor)
            .padding(.horizontal, .spacing2)
            .padding(.vertical, .spacing1)
            .background(statusBackgroundColor)
            .clipShape(RoundedRectangle(cornerRadius: .radiusSmall))
    }
    
    private var statusText: String {
        switch status {
        case .pending:
            return "ממתין"
        case .approved:
            return "אושר"
        case .rejected:
            return "נדחה"
        }
    }
    
    private var statusColor: Color {
        switch status {
        case .pending:
            return .orange
        case .approved:
            return .green
        case .rejected:
            return .red
        }
    }
    
    private var statusBackgroundColor: Color {
        switch status {
        case .pending:
            return .orange.opacity(0.1)
        case .approved:
            return .green.opacity(0.1)
        case .rejected:
            return .red.opacity(0.1)
        }
    }
}

// MARK: - Category Badge
struct CategoryBadge: View {
    let category: ExpenseCategory
    
    var body: some View {
        Text(categoryText)
            .font(.hebrewCaption)
            .foregroundColor(.mutedForeground)
            .padding(.horizontal, .spacing2)
            .padding(.vertical, .spacing1)
            .background(Color.mutedForeground.opacity(0.1))
            .clipShape(RoundedRectangle(cornerRadius: .radiusSmall))
    }
    
    private var categoryText: String {
        switch category {
        case .officeSupplies:
            return "ציוד משרדי"
        case .travel:
            return "נסיעות"
        case .utilities:
            return "שירותים"
        case .rent:
            return "שכירות"
        case .professionalServices:
            return "שירותים מקצועיים"
        case .marketing:
            return "שיווק"
        case .equipment:
            return "ציוד"
        case .other:
            return "אחר"
        }
    }
}
