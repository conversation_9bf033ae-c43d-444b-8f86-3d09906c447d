import SwiftUI

struct CreateExpenseView: View {
    @ObservedObject var expenseViewModel: ExpenseViewModel
    @Environment(\.dismiss) private var dismiss

    var body: some View {
        NavigationView {
            VStack(spacing: .spacing6) {
                if expenseViewModel.isProcessingDocument {
                    // Processing State
                    DocumentProcessingView(
                        selectedImage: expenseViewModel.selectedImage,
                        processingRotation: expenseViewModel.processingRotation
                    )
                } else if let extractedData = expenseViewModel.extractedExpenseData {
                    // Confirmation State - Show extracted data for review
                    ExpenseConfirmationView(
                        extractedData: extractedData,
                        selectedImage: expenseViewModel.selectedImage,
                        onConfirm: {
                            Task {
                                await expenseViewModel.createExpenseFromExtractedData()
                                if expenseViewModel.isExpenseCreated {
                                    dismiss()
                                }
                            }
                        },
                        onEdit: { updatedData in
                            expenseViewModel.extractedExpenseData = updatedData
                        }
                    )
                } else {
                    // Initial State - Document capture/upload
                    DocumentCaptureView(
                        onScanReceipt: {
                            expenseViewModel.showingImageSourceSelection = true
                        },
                        onUploadDocument: {
                            expenseViewModel.showingDocumentPicker = true
                        }
                    )
                }
            }
            .padding(.spacing4)
            .background(Color.background)
            .navigationBarHidden(true)
            .sheet(isPresented: $expenseViewModel.showingImageSourceSelection) {
                ImageSourceActionSheet(
                    showingCamera: $expenseViewModel.showingCamera,
                    showingPhotoLibrary: $expenseViewModel.showingPhotoLibrary,
                    showingDocumentPicker: $expenseViewModel.showingDocumentPicker,
                    isPresented: $expenseViewModel.showingImageSourceSelection
                )
            }
            .sheet(isPresented: $expenseViewModel.showingCamera) {
                CameraPickerView(
                    selectedImage: $expenseViewModel.selectedImage,
                    isPresented: $expenseViewModel.showingCamera
                )
            }
            .sheet(isPresented: $expenseViewModel.showingPhotoLibrary) {
                PhotoLibraryPickerView(
                    selectedImage: $expenseViewModel.selectedImage,
                    isPresented: $expenseViewModel.showingPhotoLibrary
                )
            }
            .sheet(isPresented: $expenseViewModel.showingDocumentPicker) {
                DocumentPickerView(
                    selectedDocumentURL: $expenseViewModel.selectedDocumentURL,
                    isPresented: $expenseViewModel.showingDocumentPicker
                )
            }
        }
        .environment(\.layoutDirection, .rightToLeft)
        .onChange(of: expenseViewModel.selectedImage) { newImage in
            if let image = newImage {
                Task {
                    await expenseViewModel.processDocumentWithAI(image: image)
                }
            }
        }
        .onChange(of: expenseViewModel.selectedDocumentURL) { newURL in
            if let url = newURL {
                Task {
                    await expenseViewModel.processDocumentWithAI(documentUrl: url)
                }
            }
        }
        .alert("שגיאה", isPresented: .constant(expenseViewModel.errorMessage != nil)) {
            Button("אישור") {
                expenseViewModel.clearMessages()
            }
        } message: {
            Text(expenseViewModel.errorMessage ?? "")
        }

    }
}

// MARK: - Document Capture View
struct DocumentCaptureView: View {
    let onScanReceipt: () -> Void
    let onUploadDocument: () -> Void

    var body: some View {
        VStack(spacing: .spacing8) {
            // Header
            VStack(spacing: .spacing4) {
                Image(systemName: "doc.text.viewfinder")
                    .font(.system(size: 80))
                    .foregroundColor(.primary.opacity(0.8))

                Text("סרוק או העלה קבלה")
                    .font(.hebrewHeading.weight(.bold))
                    .foregroundColor(.cardForeground)

                Text("הבינה המלאכותית תחלץ את פרטי ההוצאה אוטומטית")
                    .font(.hebrewBody)
                    .foregroundColor(.mutedForeground)
                    .multilineTextAlignment(.center)
            }

            // Action Buttons
            VStack(spacing: .spacing4) {
                // Scan Receipt Button
                Button(action: onScanReceipt) {
                    HStack(spacing: .spacing4) {
                        Image(systemName: "camera.viewfinder")
                            .font(.title2)

                        VStack(alignment: .trailing, spacing: .spacing1) {
                            Text("סרוק קבלה")
                                .font(.hebrewBody.weight(.medium))
                            Text("צלם קבלה עם המצלמה")
                                .font(.hebrewCaption)
                                .opacity(0.8)
                        }

                        Spacer()

                        Image(systemName: "chevron.left")
                            .font(.caption)
                            .opacity(0.6)
                    }
                    .foregroundColor(.cardForeground)
                    .padding(.spacing4)
                    .background(Color.card)
                    .clipShape(RoundedRectangle(cornerRadius: .radiusMedium))
                    .overlay(
                        RoundedRectangle(cornerRadius: .radiusMedium)
                            .stroke(Color.border, lineWidth: 1)
                    )
                }

                // Upload Document Button
                Button(action: onUploadDocument) {
                    HStack(spacing: .spacing4) {
                        Image(systemName: "doc.badge.plus")
                            .font(.title2)

                        VStack(alignment: .trailing, spacing: .spacing1) {
                            Text("העלה מסמך")
                                .font(.hebrewBody.weight(.medium))
                            Text("בחר קובץ PDF או תמונה")
                                .font(.hebrewCaption)
                                .opacity(0.8)
                        }

                        Spacer()

                        Image(systemName: "chevron.left")
                            .font(.caption)
                            .opacity(0.6)
                    }
                    .foregroundColor(.cardForeground)
                    .padding(.spacing4)
                    .background(Color.card)
                    .clipShape(RoundedRectangle(cornerRadius: .radiusMedium))
                    .overlay(
                        RoundedRectangle(cornerRadius: .radiusMedium)
                            .stroke(Color.border, lineWidth: 1)
                    )
                }
            }

            Spacer()
        }
    }
}

// MARK: - Document Processing View
struct DocumentProcessingView: View {
    let selectedImage: UIImage?
    let processingRotation: Double

    var body: some View {
        VStack(spacing: .spacing8) {
            // Processing Animation
            VStack(spacing: .spacing6) {
                ZStack {
                    Circle()
                        .stroke(Color.border, lineWidth: 4)
                        .frame(width: 80, height: 80)

                    Circle()
                        .trim(from: 0, to: 0.7)
                        .stroke(Color.primary, lineWidth: 4)
                        .frame(width: 80, height: 80)
                        .rotationEffect(.degrees(processingRotation))
                        .animation(.linear(duration: 1).repeatForever(autoreverses: false), value: processingRotation)

                    Image(systemName: "brain.head.profile")
                        .font(.title)
                        .foregroundColor(.primary)
                }

                Text("מעבד מסמך...")
                    .font(.hebrewHeading.weight(.bold))
                    .foregroundColor(.cardForeground)

                Text("הבינה המלאכותית מנתחת את הקבלה ומחלצת את הפרטים")
                    .font(.hebrewBody)
                    .foregroundColor(.mutedForeground)
                    .multilineTextAlignment(.center)
            }

            // Document Preview (if available)
            if let selectedImage = selectedImage {
                VStack(spacing: .spacing3) {
                    Text("מסמך שנסרק")
                        .font(.hebrewBody.weight(.medium))
                        .foregroundColor(.cardForeground)

                    Image(uiImage: selectedImage)
                        .resizable()
                        .aspectRatio(contentMode: .fit)
                        .frame(maxHeight: 200)
                        .clipShape(RoundedRectangle(cornerRadius: .radiusMedium))
                        .overlay(
                            RoundedRectangle(cornerRadius: .radiusMedium)
                                .stroke(Color.border, lineWidth: 1)
                        )
                }
            }

            Spacer()
        }
    }
}

// MARK: - Expense Confirmation View
struct ExpenseConfirmationView: View {
    @State var extractedData: ExtractedExpenseData
    let selectedImage: UIImage?
    let onConfirm: () -> Void
    let onEdit: (ExtractedExpenseData) -> Void

    var body: some View {
        ScrollView {
            VStack(spacing: .spacing6) {
                // Header
                VStack(spacing: .spacing2) {
                    Text("אשר פרטי ההוצאה")
                        .font(.hebrewHeading.weight(.bold))
                        .foregroundColor(.cardForeground)

                    Text("בדוק ועדכן את הפרטים שחולצו מהמסמך")
                        .font(.hebrewBody)
                        .foregroundColor(.mutedForeground)
                        .multilineTextAlignment(.center)
                }

                // Document Preview
                if let selectedImage = selectedImage {
                    VStack(spacing: .spacing3) {
                        Text("מסמך מקורי")
                            .font(.hebrewBody.weight(.medium))
                            .foregroundColor(.cardForeground)

                        Image(uiImage: selectedImage)
                            .resizable()
                            .aspectRatio(contentMode: .fit)
                            .frame(maxHeight: 150)
                            .clipShape(RoundedRectangle(cornerRadius: .radiusMedium))
                            .overlay(
                                RoundedRectangle(cornerRadius: .radiusMedium)
                                    .stroke(Color.border, lineWidth: 1)
                            )
                    }
                }

                // Extracted Data Form
                VStack(spacing: .spacing5) {
                    // Vendor Name
                    VStack(alignment: .trailing, spacing: .spacing2) {
                        Text("שם הספק")
                            .font(.hebrewBody.weight(.medium))
                            .foregroundColor(.cardForeground)

                        TextField("הזן שם ספק", text: $extractedData.vendorName)
                            .textFieldStyle(CosmicTextFieldStyle())
                            .multilineTextAlignment(.trailing)
                            .onChange(of: extractedData.vendorName) { _ in
                                onEdit(extractedData)
                            }
                    }

                    // Amount
                    VStack(alignment: .trailing, spacing: .spacing2) {
                        Text("סכום כולל")
                            .font(.hebrewBody.weight(.medium))
                            .foregroundColor(.cardForeground)

                        TextField("0.00", text: $extractedData.totalAmount)
                            .textFieldStyle(CosmicTextFieldStyle())
                            .keyboardType(.decimalPad)
                            .multilineTextAlignment(.trailing)
                            .onChange(of: extractedData.totalAmount) { _ in
                                onEdit(extractedData)
                            }
                    }

                    // Date
                    VStack(alignment: .trailing, spacing: .spacing2) {
                        Text("תאריך ההוצאה")
                            .font(.hebrewBody.weight(.medium))
                            .foregroundColor(.cardForeground)

                        DatePicker("", selection: $extractedData.date, displayedComponents: .date)
                            .datePickerStyle(.compact)
                            .environment(\.locale, Locale(identifier: "he_IL"))
                            .onChange(of: extractedData.date) { _ in
                                onEdit(extractedData)
                            }
                    }

                    // Category
                    VStack(alignment: .trailing, spacing: .spacing2) {
                        Text("קטגוריה")
                            .font(.hebrewBody.weight(.medium))
                            .foregroundColor(.cardForeground)

                        Picker("קטגוריה", selection: $extractedData.category) {
                            ForEach(ExpenseCategory.allCases, id: \.self) { category in
                                Text(getCategoryDisplayText(category))
                                    .tag(category)
                            }
                        }
                        .pickerStyle(.menu)
                        .frame(maxWidth: .infinity, alignment: .trailing)
                        .onChange(of: extractedData.category) { _ in
                            onEdit(extractedData)
                        }
                    }

                    // Description
                    VStack(alignment: .trailing, spacing: .spacing2) {
                        Text("תיאור")
                            .font(.hebrewBody.weight(.medium))
                            .foregroundColor(.cardForeground)

                        TextField("תיאור ההוצאה", text: $extractedData.description)
                            .textFieldStyle(CosmicTextFieldStyle())
                            .lineLimit(3)
                            .multilineTextAlignment(.trailing)
                            .onChange(of: extractedData.description) { _ in
                                onEdit(extractedData)
                            }
                    }
                }

                // Action Buttons
                VStack(spacing: .spacing3) {
                    Button(action: onConfirm) {
                        HStack {
                            Image(systemName: "checkmark.circle.fill")
                                .foregroundColor(.white)

                            Text("אשר וצור הוצאה")
                                .font(.hebrewBody.weight(.medium))
                                .foregroundColor(.white)
                        }
                        .frame(maxWidth: .infinity)
                        .padding(.spacing3)
                        .background(Color.primary)
                        .clipShape(RoundedRectangle(cornerRadius: .radiusMedium))
                    }
                }
            }
            .padding(.spacing4)
        }
    }

    private func getCategoryDisplayText(_ category: ExpenseCategory) -> String {
        switch category {
        case .officeSupplies:
            return "ציוד משרדי"
        case .travel:
            return "נסיעות"
        case .utilities:
            return "שירותים"
        case .rent:
            return "שכירות"
        case .professionalServices:
            return "שירותים מקצועיים"
        case .marketing:
            return "שיווק"
        case .equipment:
            return "ציוד"
        case .other:
            return "אחר"
        }
    }
}
