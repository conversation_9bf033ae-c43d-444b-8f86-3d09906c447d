import SwiftUI

struct MainTabView: View {
    @EnvironmentObject var authViewModel: AuthViewModel
    @State private var selectedTab = 0
    
    var body: some View {
        TabView(selection: $selectedTab) {
            DashboardView(selectedTab: $selectedTab)
                .tabItem {
                    Image(systemName: "house.fill")
                    Text("בית")
                        .font(.hebrewCaption)
                }
                .tag(0)
            
            DocumentsView()
                .tabItem {
                    Image(systemName: "doc.text.fill")
                    Text("מסמכים")
                        .font(.hebrewCaption)
                }
                .tag(1)
            
            CustomersView()
                .tabItem {
                    Image(systemName: "person.2.fill")
                    Text("לקוחות")
                        .font(.hebrewCaption)
                }
                .tag(2)
            
            // TODO: Re-enable for full launch - Expenses feature temporarily hidden for MVP
            // ExpensesView()
            //     .tabItem {
            //         Image(systemName: "creditcard.fill")
            //         Text("הוצאות")
            //             .font(.hebrewCaption)
            //     }
            //     .tag(3)
            
            SettingsView()
                .tabItem {
                    Image(systemName: "gearshape.fill")
                    Text("הגדרות")
                        .font(.hebrewCaption)
                }
                .tag(4)
        }
        .tint(.primary)
        .environment(\.layoutDirection, .rightToLeft)
    }
}

struct DashboardView: View {
    @EnvironmentObject var authViewModel: AuthViewModel
    @StateObject private var dashboardViewModel = DashboardViewModel()
    @Binding var selectedTab: Int

    var body: some View {
        NavigationView {
            ScrollView {
                VStack(spacing: .spacing6) {
                    // Header with company info
                    DashboardHeader()
                    
                    // Stats Cards
                    StatsCardsGrid()
                    
                    // Quick Actions
                    QuickActionsSection(selectedTab: $selectedTab)
                }
                .padding(.spacing4)
            }
            .background(Color.background)
            .navigationTitle("לוח בקרה")
            .navigationBarTitleDisplayMode(.large)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    NotificationButton()
                }
                
                ToolbarItem(placement: .navigationBarLeading) {
                    ProfileButton()
                }
            }
        }
        .environmentObject(dashboardViewModel)
        .onAppear {
            // Set the company ID and load dashboard data
            if let selectedCompany = authViewModel.selectedCompany {
                dashboardViewModel.setCompanyId(selectedCompany.id)
            }
            dashboardViewModel.loadDashboardData()
        }
    }
}

struct DashboardHeader: View {
    @EnvironmentObject var authViewModel: AuthViewModel
    
    var body: some View {
        VStack(alignment: .trailing, spacing: .spacing2) {
            if let company = authViewModel.selectedCompany {
                Text("שלום, \(company.name)")
                    .font(.hebrewHeading.weight(.semibold))
                    .foregroundColor(.cardForeground)

                Text("ח.פ: \(company.businessNumber)")
                    .font(.hebrewCaption)
                    .foregroundColor(.mutedForeground)
            }
        }
        .frame(maxWidth: .infinity, alignment: .trailing)
        .padding(.spacing4)
        .cosmicCard()
    }
}

struct StatsCardsGrid: View {
    @EnvironmentObject var dashboardViewModel: DashboardViewModel
    
    var body: some View {
        LazyVGrid(columns: [
            GridItem(.flexible()),
            GridItem(.flexible())
        ], spacing: .spacing4) {
            StatCard(
                title: "הכנסות החודש",
                value: dashboardViewModel.monthlyRevenue,
                subtitle: dashboardViewModel.revenueChange,
                icon: "chart.line.uptrend.xyaxis",
                color: .success
            )
            
            StatCard(
                title: "מע״מ לתשלום",
                value: dashboardViewModel.vatLiability,
                subtitle: dashboardViewModel.nextPaymentDate,
                icon: "banknote",
                color: .warning
            )
            
            StatCard(
                title: "חשבוניות פתוחות",
                value: "\(dashboardViewModel.openInvoicesCount)",
                subtitle: dashboardViewModel.openInvoicesAmount,
                icon: "doc.text",
                color: .primary
            )
            
            StatCard(
                title: "הוצאות ממתינות",
                value: "\(dashboardViewModel.pendingExpensesCount)",
                subtitle: "לאישור",
                icon: "exclamationmark.circle",
                color: .destructive
            )
        }
    }
}

struct StatCard: View {
    let title: String
    let value: String
    let subtitle: String
    let icon: String
    let color: Color
    
    var body: some View {
        VStack(alignment: .trailing, spacing: .spacing3) {
            HStack {
                Image(systemName: icon)
                    .foregroundColor(color)
                    .font(.title3)
                
                Spacer()
            }
            
            VStack(alignment: .trailing, spacing: .spacing1) {
                Text(value)
                    .font(.hebrewHeading.weight(.bold))
                    .foregroundColor(.cardForeground)
                
                Text(title)
                    .font(.hebrewCaption)
                    .foregroundColor(.mutedForeground)
                
                Text(subtitle)
                    .font(.caption2)
                    .foregroundColor(color)
            }
        }
        .padding(.spacing4)
        .cosmicCard()
    }
}

struct QuickActionsSection: View {
    @State private var showingDocumentCreation = false
    @Binding var selectedTab: Int

    var body: some View {
        VStack(alignment: .trailing, spacing: .spacing4) {
            Text("פעולות מהירות")
                .font(.hebrewHeading.weight(.semibold))
                .foregroundColor(.cardForeground)
            
            LazyVGrid(columns: [
                GridItem(.flexible()),
                GridItem(.flexible())
            ], spacing: .spacing4) {
                QuickActionButton(
                    title: "חשבונית חדשה",
                    icon: "doc.text.fill",
                    color: .primary
                ) {
                    showingDocumentCreation = true
                }
                
                // TODO: Re-enable for full launch - Expenses feature temporarily hidden for MVP
                // QuickActionButton(
                //     title: "סרוק הוצאה",
                //     icon: "camera.fill",
                //     color: Color.cosmicAccent
                // ) {
                //     // Navigate to scan expense
                // }
                
                QuickActionButton(
                    title: "לקוח חדש",
                    icon: "person.fill.badge.plus",
                    color: .success
                ) {
                    // Navigate to customers tab
                    selectedTab = 2
                }
                
                // TODO: Re-enable for full launch - Reports feature not yet available
                // QuickActionButton(
                //     title: "דוחות",
                //     icon: "chart.bar.fill",
                //     color: .cosmicAccent
                // ) {
                //     // Navigate to reports
                // }
            }
        }
        .padding(.spacing4)
        .cosmicCard()
        .sheet(isPresented: $showingDocumentCreation) {
            DocumentCreationWizardView()
        }
    }
}

struct QuickActionButton: View {
    let title: String
    let icon: String
    let color: Color
    let action: () -> Void
    
    var body: some View {
        Button(action: action) {
            VStack(spacing: .spacing3) {
                Image(systemName: icon)
                    .font(.title2)
                    .foregroundColor(color)
                
                Text(title)
                    .font(.hebrewCaption.weight(.medium))
                    .foregroundColor(.cardForeground)
                    .multilineTextAlignment(.center)
            }
            .frame(maxWidth: .infinity)
            .padding(.spacing4)
            .background(Color.secondary)
            .overlay(
                RoundedRectangle(cornerRadius: .radiusDefault)
                    .stroke(Color.border, lineWidth: 1)
            )
            .clipShape(RoundedRectangle(cornerRadius: .radiusDefault))
        }
        .buttonStyle(PlainButtonStyle())
    }
}

struct RecentDocumentsSection: View {
    @EnvironmentObject var dashboardViewModel: DashboardViewModel
    
    var body: some View {
        VStack(alignment: .trailing, spacing: .spacing4) {
            HStack {
                Button("הצג הכל") {
                    // Navigate to documents
                }
                .font(.hebrewCaption)
                .foregroundColor(.primary)
                
                Spacer()
                
                Text("מסמכים אחרונים")
                    .font(.hebrewHeading.weight(.semibold))
                    .foregroundColor(.cardForeground)
            }
            
            LazyVStack(spacing: .spacing3) {
                ForEach(dashboardViewModel.recentDocuments) { document in
                    DocumentRowView(document: document)
                }
            }
        }
        .padding(.spacing4)
        .cosmicCard()
    }
}

struct DocumentRowView: View {
    let document: DocumentSummary
    
    var body: some View {
        HStack {
            VStack(alignment: .leading, spacing: .spacing1) {
                Text(document.statusText)
                    .font(.caption2)
                    .foregroundColor(document.statusColor)
                
                Text(document.formattedDate)
                    .font(.caption2)
                    .foregroundColor(.mutedForeground)
            }
            
            Spacer()
            
            VStack(alignment: .trailing, spacing: .spacing1) {
                Text(document.documentNumber)
                    .font(.hebrewCaption.weight(.medium))
                    .foregroundColor(.cardForeground)
                
                Text(document.customerName)
                    .font(.caption2)
                    .foregroundColor(.mutedForeground)
                
                Text(document.formattedAmount)
                    .font(.hebrewCaption.weight(.semibold))
                    .foregroundColor(.cardForeground)
            }
        }
        .padding(.spacing3)
        .background(Color.muted.opacity(0.3))
        .clipShape(RoundedRectangle(cornerRadius: .radiusSmall))
    }
}

struct NotificationButton: View {
    @EnvironmentObject var dashboardViewModel: DashboardViewModel
    
    var body: some View {
        Button(action: {
            // Show notifications
        }) {
            ZStack {
                Image(systemName: "bell")
                    .foregroundColor(.primary)
                
                if dashboardViewModel.pendingExpensesCount > 0 {
                    Circle()
                        .fill(Color.destructive)
                        .frame(width: 8, height: 8)
                        .offset(x: 8, y: -8)
                }
            }
        }
    }
}

struct ProfileButton: View {
    @EnvironmentObject var authViewModel: AuthViewModel
    
    var body: some View {
        Button(action: {
            // Show profile menu
        }) {
            Image(systemName: "person.circle")
                .foregroundColor(.primary)
        }
    }
}

#Preview {
    MainTabView()
        .environmentObject(AuthViewModel())
}
