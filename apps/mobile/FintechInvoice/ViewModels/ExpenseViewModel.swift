import Foundation
import SwiftUI
import UIKit

@MainActor
class ExpenseViewModel: ObservableObject {
    // MARK: - Published Properties
    @Published var expenses: [Expense] = []
    @Published var isLoading = false
    @Published var errorMessage: String?
    @Published var successMessage: String?
    
    // Form data
    @Published var expenseFormData = ExpenseFormData()
    
    // UI state
    @Published var showingCreateExpense = false
    @Published var showingImageSourceSelection = false
    @Published var showingCamera = false
    @Published var showingPhotoLibrary = false
    @Published var showingDocumentPicker = false
    @Published var isUploading = false

    // AI Processing state
    @Published var isProcessingDocument = false
    @Published var processingRotation: Double = 0
    @Published var extractedExpenseData: ExtractedExpenseData?
    @Published var selectedImage: UIImage?
    @Published var selectedDocumentURL: URL?
    @Published var isExpenseCreated = false

    // Camera permissions
    @Published var cameraPermissionHelper = CameraPermissionHelper()
    
    private let supabaseService = SupabaseService.shared
    private var companyId: String?
    
    // MARK: - Initialization
    
    func setCompanyId(_ companyId: String) {
        self.companyId = companyId
    }
    
    private func getCompanyId() -> String? {
        return companyId
    }
    
    // MARK: - Data Loading
    
    func loadExpenses() {
        guard let companyId = getCompanyId() else {
            errorMessage = "מזהה חברה לא נמצא"
            return
        }
        
        isLoading = true
        errorMessage = nil
        
        Task {
            do {
                let loadedExpenses = try await supabaseService.getExpenses(companyId: companyId)
                
                await MainActor.run {
                    self.expenses = loadedExpenses
                    self.isLoading = false
                }
            } catch {
                await MainActor.run {
                    self.errorMessage = "שגיאה בטעינת הוצאות: \(error.localizedDescription)"
                    self.isLoading = false
                }
            }
        }
    }
    
    // MARK: - Expense Creation
    
    func createExpenseFromCamera() {
        cameraPermissionHelper.checkCameraPermission()
        if cameraPermissionHelper.hasPermission {
            showingCamera = true
        }
    }
    
    func createExpenseFromUpload() {
        showingImageSourceSelection = true
    }
    
    func handleImageSelected(_ image: UIImage) {
        expenseFormData.selectedImage = image
        expenseFormData.selectedDocumentURL = nil
        showingCreateExpense = true
    }
    
    func handleDocumentSelected(_ url: URL) {
        expenseFormData.selectedDocumentURL = url
        expenseFormData.selectedImage = nil
        showingCreateExpense = true
    }
    
    func createExpense() {
        guard let companyId = getCompanyId() else {
            errorMessage = "מזהה חברה לא נמצא"
            return
        }
        
        guard expenseFormData.isValid else {
            errorMessage = "אנא מלא את כל השדות הנדרשים"
            return
        }
        
        isLoading = true
        isUploading = true
        errorMessage = nil
        
        Task {
            do {
                // First, upload the file if there is one
                var fileUrl: String?
                
                if let image = expenseFormData.selectedImage {
                    fileUrl = try await supabaseService.uploadExpenseImage(image, companyId: companyId)
                } else if let documentURL = expenseFormData.selectedDocumentURL {
                    fileUrl = try await supabaseService.uploadExpenseDocument(documentURL, companyId: companyId)
                }
                
                // Create the expense request
                let request = expenseFormData.toCreateRequest(companyId: companyId, fileUrl: fileUrl)
                
                // Create the expense
                let newExpense = try await supabaseService.createExpense(request)
                
                await MainActor.run {
                    // Add to expenses list
                    self.expenses.insert(newExpense, at: 0)
                    
                    // Reset form and UI state
                    self.expenseFormData.reset()
                    self.showingCreateExpense = false
                    self.isLoading = false
                    self.isUploading = false
                    self.successMessage = "הוצאה נוצרה בהצלחה"
                    
                    // Clear success message after 3 seconds
                    DispatchQueue.main.asyncAfter(deadline: .now() + 3) {
                        self.successMessage = nil
                    }
                }
            } catch {
                await MainActor.run {
                    self.errorMessage = "שגיאה ביצירת הוצאה: \(error.localizedDescription)"
                    self.isLoading = false
                    self.isUploading = false
                }
            }
        }
    }
    
    func cancelExpenseCreation() {
        expenseFormData.reset()
        showingCreateExpense = false
        errorMessage = nil
    }
    
    // MARK: - Helper Methods
    
    func refreshExpenses() {
        loadExpenses()
    }
    
    func clearMessages() {
        errorMessage = nil
        successMessage = nil
    }
    
    // MARK: - Computed Properties
    
    var pendingExpensesCount: Int {
        expenses.filter { $0.status == .pending }.count
    }
    
    var totalExpensesAmount: Double {
        expenses.filter { $0.status == .approved }.reduce(0) { $0 + $1.totalAmount }
    }
    
    var formattedTotalAmount: String {
        let formatter = NumberFormatter()
        formatter.numberStyle = .currency
        formatter.currencyCode = "ILS"
        formatter.currencySymbol = "₪"
        return formatter.string(from: NSNumber(value: totalExpensesAmount)) ?? "₪0"
    }

    // MARK: - AI Document Processing

    func processDocumentWithAI(image: UIImage) async {
        isProcessingDocument = true
        startProcessingAnimation()
        errorMessage = nil

        do {
            let extractedData = try await AIDocumentService.shared.processDocument(image: image)
            self.extractedExpenseData = extractedData
            print("✅ AI extracted data: \(extractedData)")
        } catch {
            self.errorMessage = "שגיאה בעיבוד המסמך: \(error.localizedDescription)"
            print("❌ AI processing error: \(error)")
        }

        isProcessingDocument = false
    }

    func processDocumentWithAI(documentUrl: URL) async {
        isProcessingDocument = true
        startProcessingAnimation()
        errorMessage = nil

        do {
            // For PDF documents, we'll need to convert to image first
            // For now, we'll show an error for unsupported formats
            if documentUrl.pathExtension.lowercased() == "pdf" {
                throw AIDocumentError.imageProcessingFailed
            }

            // Load image from URL
            let data = try Data(contentsOf: documentUrl)
            guard let image = UIImage(data: data) else {
                throw AIDocumentError.imageProcessingFailed
            }

            selectedImage = image
            let extractedData = try await AIDocumentService.shared.processDocument(image: image)
            self.extractedExpenseData = extractedData
            print("✅ AI extracted data from document: \(extractedData)")
        } catch {
            self.errorMessage = "שגיאה בעיבוד המסמך: \(error.localizedDescription)"
            print("❌ AI processing error: \(error)")
        }

        isProcessingDocument = false
    }

    func createExpenseFromExtractedData() async {
        guard let extractedData = extractedExpenseData,
              let companyId = getCompanyId() else {
            errorMessage = "נתונים חסרים ליצירת הוצאה"
            return
        }

        isUploading = true
        errorMessage = nil

        do {
            // Upload image if available
            var fileUrl: String? = nil
            if let selectedImage = selectedImage {
                fileUrl = try await supabaseService.uploadExpenseImage(selectedImage, companyId: companyId)
            }

            // Calculate amounts (assuming total includes VAT)
            let totalAmount = Double(extractedData.totalAmount) ?? 0.0
            let vatAmount = totalAmount * 0.18 / 1.18 // Extract VAT from total
            let baseAmount = totalAmount - vatAmount

            // Format date for API
            let dateFormatter = DateFormatter()
            dateFormatter.dateFormat = "yyyy-MM-dd"
            let formattedDate = dateFormatter.string(from: extractedData.date)

            // Create expense request
            let expenseRequest = CreateExpenseRequest(
                companyId: companyId,
                vendorName: extractedData.vendorName,
                expenseDate: formattedDate,
                amount: baseAmount,
                vatAmount: vatAmount,
                totalAmount: totalAmount,
                currency: "ILS",
                category: extractedData.category,
                description: extractedData.description,
                source: selectedImage != nil ? .camera : .upload,
                originalFileUrl: fileUrl
            )

            let expense = try await supabaseService.createExpense(expenseRequest)
            expenses.append(expense)

            successMessage = "ההוצאה נוצרה בהצלחה"
            isExpenseCreated = true
            resetForm()

        } catch {
            errorMessage = "שגיאה ביצירת ההוצאה: \(error.localizedDescription)"
        }

        isUploading = false
    }

    func startProcessingAnimation() {
        withAnimation(.linear(duration: 1).repeatForever(autoreverses: false)) {
            processingRotation = 360
        }
    }

    private func resetForm() {
        extractedExpenseData = nil
        selectedImage = nil
        isProcessingDocument = false
        processingRotation = 0
    }
}

// MARK: - ExpenseFormData Extension
extension ExpenseFormData {
    func updateAmountAndCalculateTotals(_ newAmount: String) {
        print("🔢 Amount input changed: '\(newAmount)'")
        amount = newAmount
        calculateTotals(with: newAmount)
    }
}
