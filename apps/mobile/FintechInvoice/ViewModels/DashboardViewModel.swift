import Foundation
import SwiftUI

@MainActor
class DashboardViewModel: ObservableObject {
    @Published var monthlyRevenue = "₪0"
    @Published var revenueChange = "+0%"
    @Published var vatLiability = "₪0"
    @Published var nextPaymentDate = "אין תשלום"
    @Published var collectableAmount = "₪0"
    @Published var collectableInvoicesCount = 0
    @Published var recentDocuments: [DocumentSummary] = []
    @Published var isLoading = false

    private let supabaseService = SupabaseService.shared
    private var companyId: String?

    func setCompanyId(_ companyId: String) {
        self.companyId = companyId
    }
    
    func loadDashboardData() {
        isLoading = true
        
        Task {
            await loadStats()
            await loadRecentDocuments()
            
            await MainActor.run {
                isLoading = false
            }
        }
    }
    
    private func loadStats() async {
        guard let companyId = getCompanyId() else {
            print("❌ No company ID available for dashboard metrics")
            return
        }

        do {
            // Call the deployed dashboard-metrics function
            let metrics = try await supabaseService.getDashboardMetrics(companyId: companyId)

            await MainActor.run {
                // Format currency values
                monthlyRevenue = formatCurrency(metrics.monthlyIncome.amount)
                revenueChange = formatChangePercent(metrics.monthlyIncome.changePercent)
                vatLiability = formatCurrency(metrics.vatLiability.amount)
                nextPaymentDate = formatDate(metrics.vatLiability.dueDate)
                collectableAmount = formatCurrency(metrics.collectableMoney.amount)
                collectableInvoicesCount = metrics.collectableMoney.invoiceCount
            }

            print("✅ Dashboard metrics loaded successfully")
        } catch {
            print("❌ Failed to load dashboard metrics: \(error.localizedDescription)")

            // Fallback to mock data if API fails
            await MainActor.run {
                monthlyRevenue = "₪45,230"
                revenueChange = "+12.5%"
                vatLiability = "₪8,141"
                nextPaymentDate = "15/02/2025"
                collectableAmount = "₪23,450"
                collectableInvoicesCount = 7
            }
        }
    }

    private func formatCurrency(_ amount: Double) -> String {
        let formatter = NumberFormatter()
        formatter.numberStyle = .currency
        formatter.currencyCode = "ILS"
        formatter.currencySymbol = "₪"
        formatter.locale = Locale(identifier: "he_IL")
        return formatter.string(from: NSNumber(value: amount)) ?? "₪\(amount)"
    }

    private func formatChangePercent(_ percent: Double) -> String {
        let sign = percent >= 0 ? "+" : ""
        return "\(sign)\(String(format: "%.1f", percent))% מהחודש הקודם"
    }

    private func formatDate(_ dateString: String) -> String {
        let formatter = DateFormatter()
        formatter.dateFormat = "yyyy-MM-dd"

        if let date = formatter.date(from: dateString) {
            formatter.dateStyle = .short
            formatter.locale = Locale(identifier: "he_IL")
            return formatter.string(from: date)
        }

        return dateString
    }
    
    private func loadRecentDocuments() async {
        guard let companyId = getCompanyId() else { return }

        do {
            let documents = try await SupabaseService.shared.getDocuments(companyId: companyId, limit: 5)

            // Convert Document objects to DocumentSummary objects
            let documentSummaries = documents.map { document in
                DocumentSummary(
                    id: document.id,
                    documentNumber: document.documentNumber,
                    customerName: "לקוח #\(document.customerId.prefix(8))", // Placeholder for customer name
                    amount: document.totalAmount,
                    status: document.status,
                    date: parseDate(document.issueDate) ?? Date()
                )
            }

            await MainActor.run {
                recentDocuments = documentSummaries
            }
        } catch {
            print("❌ Failed to load recent documents: \(error.localizedDescription)")
            await MainActor.run {
                recentDocuments = []
            }
        }
    }

    private func getCompanyId() -> String? {
        return companyId
    }

    private func parseDate(_ dateString: String) -> Date? {
        let formatter = DateFormatter()
        formatter.dateFormat = "yyyy-MM-dd"
        return formatter.date(from: dateString)
    }
}

// MARK: - Data Models
struct DocumentSummary: Identifiable {
    let id: String
    let documentNumber: String
    let customerName: String
    let amount: Double
    let status: DocumentStatus
    let date: Date
    
    var formattedAmount: String {
        let formatter = NumberFormatter()
        formatter.numberStyle = .currency
        formatter.currencyCode = "ILS"
        formatter.currencySymbol = "₪"
        return formatter.string(from: NSNumber(value: amount)) ?? "₪\(amount)"
    }
    
    var formattedDate: String {
        let formatter = DateFormatter()
        formatter.dateStyle = .short
        formatter.locale = Locale(identifier: "he_IL")
        return formatter.string(from: date)
    }
    
    var statusText: String {
        switch status {
        case .draft:
            return "טיוטה"
        case .pendingAllocation:
            return "ממתין להקצאה"
        case .approved:
            return "מאושר"
        case .sent:
            return "נשלח"
        case .paid:
            return "שולם"
        case .cancelled:
            return "בוטל"
        }
    }
    
    var statusColor: Color {
        switch status {
        case .draft:
            return .mutedForeground
        case .pendingAllocation:
            return .warning
        case .approved:
            return .primary
        case .sent:
            return .primary
        case .paid:
            return .success
        case .cancelled:
            return .destructive
        }
    }
}
