import { createFileRoute, useNavigate } from '@tanstack/react-router'
import { Button } from '@fintech/ui'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@fintech/ui'
import { Plus, Users } from 'lucide-react'
import { useState, useEffect } from 'react'

interface StatCardProps {
  title: string
  value: string
  subtitle?: string
  change?: string
  action?: () => void
  actionText?: string
}

interface DashboardMetrics {
  monthly_income: {
    amount: number
    change_percent: number
    period: string
  }
  vat_liability: {
    amount: number
    due_date: string
  }
  collectable_money: {
    amount: number
    invoice_count: number
  }
}

function StatCard({ title, value, subtitle, change, action, actionText }: StatCardProps) {
  return (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
        <CardTitle className="text-sm font-medium">{title}</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="text-2xl font-bold">{value}</div>
        {subtitle && (
          <p className="text-xs text-muted-foreground">{subtitle}</p>
        )}
        {change && (
          <p className="text-xs text-green-600 mt-1">{change}</p>
        )}
        {action && actionText && (
          <Button 
            variant="outline" 
            size="sm" 
            className="mt-2"
            onClick={action}
          >
            {actionText}
          </Button>
        )}
      </CardContent>
    </Card>
  )
}

function DashboardIndex() {
  const navigate = useNavigate()
  const [metrics, setMetrics] = useState<DashboardMetrics | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  // Mock company ID - in real app this would come from auth context
  const companyId = 'mock-company-id'

  useEffect(() => {
    loadDashboardMetrics()
  }, [])

  const loadDashboardMetrics = async () => {
    try {
      setIsLoading(true)
      setError(null)

      // Call the deployed dashboard-metrics function
      const response = await fetch(`https://zhwqtgypoueykwgphmqn.supabase.co/functions/v1/dashboard-metrics?company_id=${companyId}`, {
        headers: {
          'Authorization': `Bearer ${process.env.REACT_APP_SUPABASE_ANON_KEY || 'your-anon-key-here'}`,
          'Content-Type': 'application/json'
        }
      })

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`)
      }

      const data = await response.json()

      if (data.success) {
        setMetrics(data.data)
      } else {
        throw new Error(data.error || 'Failed to fetch dashboard metrics')
      }

      setIsLoading(false)
    } catch (err) {
      console.error('Dashboard metrics error:', err)
      // Fallback to mock data if API fails
      setMetrics({
        monthly_income: {
          amount: 45231.50,
          change_percent: 12.5,
          period: '1/2025'
        },
        vat_liability: {
          amount: 8142.27,
          due_date: '2025-02-15'
        },
        collectable_money: {
          amount: 23450.00,
          invoice_count: 7
        }
      })
      setError('שגיאה בטעינת נתוני הדשבורד - מציג נתונים דמה')
      setIsLoading(false)
    }
  }

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('he-IL', {
      style: 'currency',
      currency: 'ILS',
      currencyDisplay: 'symbol'
    }).format(amount).replace('ILS', '₪')
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('he-IL')
  }

  const stats = metrics ? [
    {
      title: 'הכנסות החודש',
      value: formatCurrency(metrics.monthly_income.amount),
      change: `${metrics.monthly_income.change_percent >= 0 ? '+' : ''}${metrics.monthly_income.change_percent}% מהחודש הקודם`,
      subtitle: `${metrics.monthly_income.period}`
    },
    {
      title: 'מע"מ לתשלום',
      value: formatCurrency(metrics.vat_liability.amount),
      subtitle: `תאריך תשלום: ${formatDate(metrics.vat_liability.due_date)}`
    },
    {
      title: 'כסף לגביה',
      value: formatCurrency(metrics.collectable_money.amount),
      subtitle: `${metrics.collectable_money.invoice_count} חשבוניות פתוחות`
    }
  ] : []

  if (isLoading) {
    return (
      <div className="p-6">
        <div className="flex items-center justify-center min-h-[400px]">
          <div className="text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
            <p className="text-muted-foreground">טוען נתוני דשבורד...</p>
          </div>
        </div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="p-6">
        <div className="flex items-center justify-center min-h-[400px]">
          <div className="text-center">
            <p className="text-destructive mb-4">{error}</p>
            <Button onClick={loadDashboardMetrics}>נסה שוב</Button>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="p-6">
      <div className="flex items-center justify-between mb-6">
        <h1 className="text-2xl font-bold">לוח בקרה</h1>
        
        {/* Quick actions */}
        <div className="flex items-center space-x-2 space-x-reverse">
          <Button
            size="sm"
            onClick={() => navigate({ to: '/dashboard/customers' })}
            variant="outline"
          >
            <Users size={16} className="ml-1" />
            לקוח חדש
          </Button>
          <Button size="sm">
            <Plus size={16} className="ml-1" />
            חשבונית חדשה
          </Button>
        </div>
      </div>
      
      {/* Stats cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {stats.map((stat, index) => (
          <StatCard key={index} {...stat} />
        ))}
      </div>
    </div>
  )
}

export const Route = createFileRoute('/dashboard/')({
  component: DashboardIndex,
})
