import { serve } from 'https://deno.land/std@0.168.0/http/server.ts';
import {
  createSupabaseClient,
  corsHeaders,
  errorResponse,
  successResponse,
  validationErrorResponse,
  handleCors,
  getUserFromToken,
  checkUserRole,
  validateBusinessNumber,
  validateVatId,
  validateEmail,
  validateIsraeliPhone,
  logAudit,
} from '../_shared/utils.ts';
import { ValidationError } from '../_shared/types.ts';

interface UpdateCustomerRequest {
  customer_id: string;
  business_number?: string;
  name_hebrew?: string;
  name_english?: string;
  vat_id?: string;
  billing_address_hebrew?: string;
  billing_address_english?: string;
  shipping_address_hebrew?: string;
  shipping_address_english?: string;
  city_hebrew?: string;
  city_english?: string;
  contact_name?: string;
  contact_email?: string;
  contact_phone?: string;
  notes?: string;
}

serve(async (req) => {
  // Handle CORS
  const corsResponse = handleCors(req);
  if (corsResponse) return corsResponse;

  try {
    if (req.method !== 'POST') {
      return errorResponse('Method not allowed', 405);
    }

    // Get user from token
    const user = await getUserFromToken(req);
    
    const body: UpdateCustomerRequest = await req.json();
    
    // Validate input
    const validationErrors: ValidationError[] = [];

    if (!body.customer_id) {
      validationErrors.push({ field: 'customer_id', message: 'Customer ID is required' });
    }

    if (body.business_number && !validateBusinessNumber(body.business_number)) {
      validationErrors.push({ field: 'business_number', message: 'Valid 9-digit business number is required' });
    }

    if (body.name_hebrew && body.name_hebrew.trim().length < 2) {
      validationErrors.push({ field: 'name_hebrew', message: 'Customer name in Hebrew must be at least 2 characters' });
    }

    if (body.billing_address_hebrew && body.billing_address_hebrew.trim().length < 5) {
      validationErrors.push({ field: 'billing_address_hebrew', message: 'Billing address must be at least 5 characters' });
    }

    if (body.city_hebrew && body.city_hebrew.trim().length < 2) {
      validationErrors.push({ field: 'city_hebrew', message: 'City name must be at least 2 characters' });
    }

    if (body.vat_id && !validateVatId(body.vat_id)) {
      validationErrors.push({ field: 'vat_id', message: 'Valid 9-digit VAT ID required' });
    }

    if (body.contact_email && !validateEmail(body.contact_email)) {
      validationErrors.push({ field: 'contact_email', message: 'Valid email address required' });
    }

    if (body.contact_phone && !validateIsraeliPhone(body.contact_phone)) {
      validationErrors.push({ field: 'contact_phone', message: 'Valid Israeli phone number required' });
    }

    if (validationErrors.length > 0) {
      return validationErrorResponse(validationErrors);
    }

    const supabase = createSupabaseClient();

    // Get existing customer to verify ownership and get company_id
    const { data: existingCustomer, error: fetchError } = await supabase
      .from('customers')
      .select('*')
      .eq('id', body.customer_id)
      .single();

    if (fetchError || !existingCustomer) {
      console.error('Customer fetch error:', fetchError);
      return errorResponse('Customer not found', 404);
    }

    // Check user access to company
    await checkUserRole(user.id, existingCustomer.company_id, ['admin', 'user', 'accountant']);

    // Check if business number is being changed and if it conflicts with existing customer
    if (body.business_number && body.business_number !== existingCustomer.business_number) {
      const { data: conflictingCustomer } = await supabase
        .from('customers')
        .select('id')
        .eq('company_id', existingCustomer.company_id)
        .eq('business_number', body.business_number)
        .neq('id', body.customer_id)
        .single();

      if (conflictingCustomer) {
        return errorResponse('Customer with this business number already exists', 409);
      }
    }

    // Prepare update data (only include fields that are provided)
    const updateData: any = {};
    
    if (body.business_number !== undefined) updateData.business_number = body.business_number;
    if (body.name_hebrew !== undefined) updateData.name_hebrew = body.name_hebrew.trim();
    if (body.name_english !== undefined) updateData.name_english = body.name_english?.trim();
    if (body.vat_id !== undefined) updateData.vat_id = body.vat_id;
    if (body.billing_address_hebrew !== undefined) updateData.billing_address_hebrew = body.billing_address_hebrew.trim();
    if (body.billing_address_english !== undefined) updateData.billing_address_english = body.billing_address_english?.trim();
    if (body.shipping_address_hebrew !== undefined) updateData.shipping_address_hebrew = body.shipping_address_hebrew?.trim();
    if (body.shipping_address_english !== undefined) updateData.shipping_address_english = body.shipping_address_english?.trim();
    if (body.city_hebrew !== undefined) updateData.city_hebrew = body.city_hebrew.trim();
    if (body.city_english !== undefined) updateData.city_english = body.city_english?.trim();
    if (body.contact_name !== undefined) updateData.contact_name = body.contact_name?.trim();
    if (body.contact_email !== undefined) updateData.contact_email = body.contact_email?.trim();
    if (body.contact_phone !== undefined) updateData.contact_phone = body.contact_phone?.trim();
    if (body.notes !== undefined) updateData.notes = body.notes?.trim();

    // Update customer
    const { data: customer, error: customerError } = await supabase
      .from('customers')
      .update(updateData)
      .eq('id', body.customer_id)
      .select()
      .single();

    if (customerError) {
      console.error('Customer update error:', customerError);
      return errorResponse('Failed to update customer', 500);
    }

    // Log audit
    await logAudit(
      existingCustomer.company_id,
      user.id,
      'update',
      'customer',
      customer.id,
      existingCustomer,
      customer,
      req
    );

    // Return response in the format expected by iOS app
    return new Response(
      JSON.stringify({
        success: true,
        data: {
          customer: customer
        },
        error: null
      }),
      {
        status: 200,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
      }
    );

  } catch (error) {
    console.error('Update customer error:', error);
    return errorResponse('Internal server error', 500);
  }
});
