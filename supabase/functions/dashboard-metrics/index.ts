import { serve } from 'https://deno.land/std@0.168.0/http/server.ts';
import {
  createSupabaseClient,
  corsHeaders,
  errorResponse,
  successResponse,
  handleCors,
  getUserFromToken,
  checkUserRole,
} from '../_shared/utils.ts';

interface DashboardMetrics {
  monthly_income: {
    amount: number;
    change_percent: number;
    period: string;
  };
  vat_liability: {
    amount: number;
    due_date: string;
  };
  collectable_money: {
    amount: number;
    invoice_count: number;
  };
}

serve(async (req) => {
  // Handle CORS
  const corsResponse = handleCors(req);
  if (corsResponse) return corsResponse;

  try {
    if (req.method !== 'GET') {
      return errorResponse('Method not allowed', 405);
    }

    // Get user from token
    const user = await getUserFromToken(req);
    
    // Get query parameters
    const url = new URL(req.url);
    const companyId = url.searchParams.get('company_id');

    if (!companyId) {
      return errorResponse('company_id is required', 400);
    }

    // Check user access to company
    await checkUserRole(user.id, companyId, ['admin', 'user', 'accountant']);

    const supabase = createSupabaseClient();

    // Get current date info
    const now = new Date();
    const currentMonth = now.getMonth() + 1;
    const currentYear = now.getFullYear();
    const previousMonth = currentMonth === 1 ? 12 : currentMonth - 1;
    const previousYear = currentMonth === 1 ? currentYear - 1 : currentYear;

    // Calculate current month dates
    const currentMonthStart = `${currentYear}-${currentMonth.toString().padStart(2, '0')}-01`;
    const currentMonthEnd = new Date(currentYear, currentMonth, 0).toISOString().split('T')[0];

    // Calculate previous month dates
    const previousMonthStart = `${previousYear}-${previousMonth.toString().padStart(2, '0')}-01`;
    const previousMonthEnd = new Date(previousYear, previousMonth + 1, 0).toISOString().split('T')[0];

    // Get current month income (VAT excluded from receipts and tax-invoice-receipts)
    const { data: currentMonthDocs, error: currentError } = await supabase
      .from('documents')
      .select('subtotal, vat_amount, total_amount, status')
      .eq('company_id', companyId)
      .in('document_type', ['receipt', 'tax_invoice_receipt'])
      .in('status', ['sent', 'paid'])
      .gte('issue_date', currentMonthStart)
      .lte('issue_date', currentMonthEnd);

    if (currentError) {
      console.error('Current month documents error:', currentError);
      return errorResponse('Failed to fetch current month data', 500);
    }

    // Get previous month income for comparison
    const { data: previousMonthDocs, error: previousError } = await supabase
      .from('documents')
      .select('subtotal, vat_amount, total_amount, status')
      .eq('company_id', companyId)
      .in('document_type', ['receipt', 'tax_invoice_receipt'])
      .in('status', ['sent', 'paid'])
      .gte('issue_date', previousMonthStart)
      .lte('issue_date', previousMonthEnd);

    if (previousError) {
      console.error('Previous month documents error:', previousError);
      return errorResponse('Failed to fetch previous month data', 500);
    }

    // Get VAT liability (from all generated documents)
    const { data: vatDocs, error: vatError } = await supabase
      .from('documents')
      .select('vat_amount')
      .eq('company_id', companyId)
      .in('document_type', ['receipt', 'tax_invoice_receipt'])
      .in('status', ['sent', 'paid'])
      .gte('issue_date', currentMonthStart)
      .lte('issue_date', currentMonthEnd);

    if (vatError) {
      console.error('VAT documents error:', vatError);
      return errorResponse('Failed to fetch VAT data', 500);
    }

    // Get unpaid invoices (collectable money)
    const { data: unpaidInvoices, error: unpaidError } = await supabase
      .from('documents')
      .select('total_amount, paid_amount')
      .eq('company_id', companyId)
      .in('document_type', ['tax_invoice', 'tax_invoice_receipt'])
      .in('status', ['sent'])
      .neq('status', 'paid');

    if (unpaidError) {
      console.error('Unpaid invoices error:', unpaidError);
      return errorResponse('Failed to fetch unpaid invoices', 500);
    }

    // Calculate metrics
    const currentMonthIncome = (currentMonthDocs || []).reduce((sum, doc) => sum + (doc.subtotal || 0), 0);
    const previousMonthIncome = (previousMonthDocs || []).reduce((sum, doc) => sum + (doc.subtotal || 0), 0);
    
    const changePercent = previousMonthIncome > 0 
      ? ((currentMonthIncome - previousMonthIncome) / previousMonthIncome) * 100 
      : 0;

    const vatLiability = (vatDocs || []).reduce((sum, doc) => sum + (doc.vat_amount || 0), 0);

    const collectableMoney = (unpaidInvoices || []).reduce((sum, invoice) => {
      const totalAmount = invoice.total_amount || 0;
      const paidAmount = invoice.paid_amount || 0;
      return sum + (totalAmount - paidAmount);
    }, 0);

    // Calculate next VAT payment date (15th of next month)
    const nextMonth = currentMonth === 12 ? 1 : currentMonth + 1;
    const nextYear = currentMonth === 12 ? currentYear + 1 : currentYear;
    const vatDueDate = `${nextYear}-${nextMonth.toString().padStart(2, '0')}-15`;

    const metrics: DashboardMetrics = {
      monthly_income: {
        amount: Math.round(currentMonthIncome * 100) / 100,
        change_percent: Math.round(changePercent * 10) / 10,
        period: `${currentMonth}/${currentYear}`
      },
      vat_liability: {
        amount: Math.round(vatLiability * 100) / 100,
        due_date: vatDueDate
      },
      collectable_money: {
        amount: Math.round(collectableMoney * 100) / 100,
        invoice_count: (unpaidInvoices || []).length
      }
    };

    return successResponse(metrics);

  } catch (error) {
    console.error('Dashboard metrics error:', error);
    return errorResponse('Internal server error', 500);
  }
});
